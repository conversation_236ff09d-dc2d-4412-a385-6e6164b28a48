require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

async function generateMockDataJune2025() {
  try {
    console.log('Connecting to database...');
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });
    console.log('Connected successfully!');

    // First, get sample data from June 21 to use as a template
    console.log('\nFetching sample data from June 21, 2025...');
    const sampleResult = await sql.query(`
      SELECT TOP 100
        PlazaName,
        PlazaCode,
        VehicleNumber,
        VehicleType,
        EntryLane,
        ExitLane,
        ParkedDuration,
        ParkingFee,
        iTotalGSTFee,
        PaymentMode,
        PaymentType
      FROM tblParkwiz_Parking_Data_OLD
      WHERE ExitDateTime >= '2025-06-21' AND ExitDateTime < '2025-06-22'
      ORDER BY NEWID()
    `);
    
    if (sampleResult.recordset.length === 0) {
      console.log('No sample data found for June 21, 2025. Cannot generate mock data.');
      await sql.close();
      return;
    }
    
    console.log(`Found ${sampleResult.recordset.length} sample records to use as templates.`);
    
    // Define the date range for which we need to generate data
    const days = ['2025-06-22', '2025-06-23', '2025-06-24', '2025-06-25', '2025-06-26', '2025-06-27'];
    
    // Get the highest PakringDataID to start incrementing from
    const maxIdResult = await sql.query(`
      SELECT MAX(PakringDataID) as MaxId FROM tblParkwiz_Parking_Data_OLD
    `);
    
    let nextId = maxIdResult.recordset[0].MaxId + 1;
    console.log(`Starting with ID: ${nextId}`);
    
    // Create a transaction for batch inserts
    const transaction = new sql.Transaction();
    await transaction.begin();
    
    try {
      let totalInserted = 0;
      
      // Generate data for each day
      for (const day of days) {
        console.log(`\nGenerating data for ${day}...`);
        
        // Determine how many records to generate for this day (random variation around 900)
        const recordCount = Math.floor(Math.random() * 200) + 800; // 800-1000 records
        console.log(`Planning to insert ${recordCount} records for ${day}`);
        
        // Prepare batch insert
        const request = new sql.Request(transaction);
        
        // Create a batch of inserts
        let insertCount = 0;
        let batchQuery = '';
        
        for (let i = 0; i < recordCount; i++) {
          // Pick a random sample record as template
          const sampleIndex = Math.floor(Math.random() * sampleResult.recordset.length);
          const sample = sampleResult.recordset[sampleIndex];
          
          // Generate random times for this day
          const hour = Math.floor(Math.random() * 24);
          const minute = Math.floor(Math.random() * 60);
          const second = Math.floor(Math.random() * 60);
          
          const exitTime = new Date(`${day}T${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}.000Z`);
          
          // Calculate entry time based on parked duration
          const parkedDuration = sample.ParkedDuration > 0 ? sample.ParkedDuration : Math.floor(Math.random() * 180) + 30; // 30-210 minutes if original is 0
          const entryTime = new Date(exitTime.getTime() - (parkedDuration * 60 * 1000));
          
          // Slightly vary the parking fee and GST
          const variationFactor = 0.8 + (Math.random() * 0.4); // 0.8 to 1.2
          const parkingFee = Math.round((sample.ParkingFee * variationFactor) * 100) / 100;
          const gstFee = Math.round((parkingFee * 0.18) * 100) / 100; // Assuming 18% GST
          
          // Construct the insert statement
          batchQuery += `
            INSERT INTO tblParkwiz_Parking_Data_OLD (
              PakringDataID, PlazaName, PlazaCode, VehicleNumber, VehicleType,
              EntryDateTime, ExitDateTime, EntryLane, ExitLane,
              ParkedDuration, ParkingFee, iTotalGSTFee, PaymentMode, PaymentType
            ) VALUES (
              ${nextId}, '${sample.PlazaName}', '${sample.PlazaCode}', '${sample.VehicleNumber}', '${sample.VehicleType}',
              '${entryTime.toISOString()}', '${exitTime.toISOString()}', '${sample.EntryLane}', '${sample.ExitLane}',
              ${parkedDuration}, ${parkingFee}, ${gstFee}, '${sample.PaymentMode}', '${sample.PaymentType}'
            );`;
          
          nextId++;
          insertCount++;
          
          // Execute in batches of 100 to avoid too large queries
          if (insertCount % 100 === 0 || i === recordCount - 1) {
            if (batchQuery) {
              await request.query(batchQuery);
              console.log(`Inserted batch of ${insertCount} records...`);
              totalInserted += insertCount;
              insertCount = 0;
              batchQuery = '';
            }
          }
        }
      }
      
      // Commit the transaction
      await transaction.commit();
      console.log(`\nSuccessfully inserted ${totalInserted} mock records for June 22-27, 2025.`);
      
    } catch (error) {
      // If there's an error, roll back the transaction
      await transaction.rollback();
      console.error('Error during data generation:', error);
      console.log('Transaction rolled back. No data was inserted.');
    }
    
    // Verify the data was inserted
    console.log('\nVerifying data insertion...');
    const verificationResult = await sql.query(`
      SELECT 
        CONVERT(date, ExitDateTime) as Date,
        COUNT(*) as Count
      FROM tblParkwiz_Parking_Data_OLD
      WHERE ExitDateTime >= '2025-06-21' AND ExitDateTime <= '2025-06-27'
      GROUP BY CONVERT(date, ExitDateTime)
      ORDER BY Date
    `);
    
    console.log('Data counts by day:');
    verificationResult.recordset.forEach(row => {
      console.log(`- ${row.Date.toISOString().split('T')[0]}: ${row.Count} records`);
    });
    
    await sql.close();
    console.log('\nDatabase connection closed.');
    
  } catch (err) {
    console.error('Error:', err);
    if (sql.connected) {
      await sql.close();
    }
  }
}

// Ask for confirmation before running
console.log('WARNING: This script will generate mock data in your database.');
console.log('Press Ctrl+C to cancel or wait 5 seconds to continue...');

setTimeout(() => {
  generateMockDataJune2025();
}, 5000);