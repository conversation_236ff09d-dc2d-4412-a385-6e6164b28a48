require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

async function testDateRangeJune2025() {
  try {
    console.log('Connecting to database...');
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });
    console.log('Connected successfully!');

    // Define the date range
    const startDate = new Date('2025-06-21T00:00:00.000Z');
    const endDate = new Date('2025-06-27T23:59:59.999Z');
    
    console.log(`\n=== Testing dashboard data from parkwizparkingdata old table ===`);
    console.log(`Date range: ${startDate.toISOString()} to ${endDate.toISOString()}`);
    
    // Create a new SQL request
    const request = new sql.Request();
    request.input('startDate', sql.DateTime, startDate);
    request.input('endDate', sql.DateTime, endDate);
    
    // 1. Get summary metrics
    const summaryResult = await request.query(`
      SELECT 
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as TotalRevenue, 
        COUNT(*) as TransactionCount,
        COUNT(DISTINCT CASE WHEN VehicleNumber <> 'NA' AND VehicleNumber IS NOT NULL THEN VehicleNumber ELSE NULL END) as VehicleCount,
        ISNULL(AVG(ISNULL(ParkedDuration, 0)), 0) as AvgDuration
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime BETWEEN @startDate AND @endDate
    `);
    
    console.log('\n1. Dashboard Summary:');
    console.log('- Total Revenue:', summaryResult.recordset[0].TotalRevenue);
    console.log('- Transaction Count:', summaryResult.recordset[0].TransactionCount);
    console.log('- Vehicle Count:', summaryResult.recordset[0].VehicleCount);
    console.log('- Average Duration (minutes):', summaryResult.recordset[0].AvgDuration);
    
    // 2. Get revenue by payment method
    const paymentMethodResult = await request.query(`
      SELECT 
        ISNULL(PaymentMode, 'Unknown') as PaymentMode, 
        COUNT(*) as TransactionCount, 
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as Revenue 
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime BETWEEN @startDate AND @endDate 
      GROUP BY PaymentMode 
      ORDER BY Revenue DESC
    `);
    
    console.log('\n2. Revenue by Payment Method:');
    paymentMethodResult.recordset.forEach(method => {
      console.log(`- ${method.PaymentMode}: ${method.TransactionCount} transactions, ₹${method.Revenue.toFixed(2)}`);
    });
    
    // 3. Get revenue by plaza
    const plazaResult = await request.query(`
      SELECT 
        PlazaName, 
        COUNT(*) as TransactionCount, 
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as Revenue 
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime BETWEEN @startDate AND @endDate 
      GROUP BY PlazaName 
      ORDER BY Revenue DESC
    `);
    
    console.log('\n3. Revenue by Plaza:');
    plazaResult.recordset.forEach(plaza => {
      console.log(`- ${plaza.PlazaName}: ${plaza.TransactionCount} transactions, ₹${plaza.Revenue.toFixed(2)}`);
    });
    
    // 4. Get peak hours data
    const peakHoursResult = await request.query(`
      SELECT
        DATEPART(HOUR, ExitDateTime) as Hour,
        COUNT(*) as Count
      FROM tblParkwiz_Parking_Data_OLD
      WHERE ExitDateTime BETWEEN @startDate AND @endDate
      GROUP BY DATEPART(HOUR, ExitDateTime)
      ORDER BY Hour
    `);
    
    console.log('\n4. Peak Hours Data:');
    // Fill in missing hours with zero counts
    const hourlyData = Array(24).fill().map((_, i) => ({
      hour: i,
      count: 0
    }));
    
    peakHoursResult.recordset.forEach(row => {
      hourlyData[row.Hour].count = row.Count;
    });
    
    hourlyData.forEach(hour => {
      console.log(`- Hour ${hour.hour.toString().padStart(2, '0')}:00: ${hour.count} transactions`);
    });
    
    // 5. Get daily breakdown
    const dailyResult = await request.query(`
      SELECT 
        CONVERT(date, ExitDateTime) as Date,
        COUNT(*) as TransactionCount,
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as Revenue
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime BETWEEN @startDate AND @endDate
      GROUP BY CONVERT(date, ExitDateTime)
      ORDER BY Date
    `);
    
    console.log('\n5. Daily Breakdown:');
    dailyResult.recordset.forEach(day => {
      console.log(`- ${day.Date.toISOString().split('T')[0]}: ${day.TransactionCount} transactions, ₹${day.Revenue.toFixed(2)}`);
    });
    
    // 6. Get sample transactions
    const sampleResult = await request.query(`
      SELECT TOP 10
        PakringDataID,
        PlazaName,
        VehicleNumber,
        EntryDateTime,
        ExitDateTime,
        EntryLane,
        ExitLane,
        ParkedDuration,
        ParkingFee,
        iTotalGSTFee,
        PaymentMode,
        PaymentType
      FROM tblParkwiz_Parking_Data_OLD
      WHERE ExitDateTime BETWEEN @startDate AND @endDate
      ORDER BY ExitDateTime DESC
    `);
    
    console.log('\n6. Sample Transactions:');
    console.log(JSON.stringify(sampleResult.recordset, null, 2));

    await sql.close();
    console.log('\nDatabase connection closed.');
  } catch (err) {
    console.error('Error:', err);
    if (sql.connected) {
      await sql.close();
    }
  }
}

testDateRangeJune2025();