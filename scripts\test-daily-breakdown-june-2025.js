require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

async function testDailyBreakdownJune2025() {
  try {
    console.log('Connecting to database...');
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });
    console.log('Connected successfully!');

    // Define the date range
    const startDate = new Date('2025-06-21T00:00:00.000Z');
    const endDate = new Date('2025-06-27T23:59:59.999Z');
    
    console.log(`\n=== Testing daily breakdown for parkwizparkingdata old table ===`);
    console.log(`Date range: ${startDate.toISOString()} to ${endDate.toISOString()}`);
    
    // Create a new SQL request
    const request = new sql.Request();
    request.input('startDate', sql.DateTime, startDate);
    request.input('endDate', sql.DateTime, endDate);
    
    // Get detailed daily breakdown
    const dailyResult = await request.query(`
      SELECT 
        CONVERT(date, ExitDateTime) as Date,
        COUNT(*) as TransactionCount,
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as Revenue,
        COUNT(DISTINCT CASE WHEN VehicleNumber <> 'NA' AND VehicleNumber IS NOT NULL THEN VehicleNumber ELSE NULL END) as VehicleCount,
        ISNULL(AVG(ISNULL(ParkedDuration, 0)), 0) as AvgDuration
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime BETWEEN @startDate AND @endDate
      GROUP BY CONVERT(date, ExitDateTime)
      ORDER BY Date
    `);
    
    console.log('\nDetailed Daily Breakdown:');
    if (dailyResult.recordset.length === 0) {
      console.log('No data found for the specified date range.');
    } else {
      dailyResult.recordset.forEach(day => {
        console.log(`\n=== ${day.Date.toISOString().split('T')[0]} ===`);
        console.log(`- Transactions: ${day.TransactionCount}`);
        console.log(`- Revenue: ₹${day.Revenue.toFixed(2)}`);
        console.log(`- Unique Vehicles: ${day.VehicleCount}`);
        console.log(`- Average Duration: ${day.AvgDuration.toFixed(2)} minutes`);
      });
    }
    
    // Check if there's data for each day in the range
    console.log('\nChecking data availability for each day:');
    const days = ['2025-06-21', '2025-06-22', '2025-06-23', '2025-06-24', '2025-06-25', '2025-06-26', '2025-06-27'];
    
    for (const day of days) {
      const dayStart = new Date(`${day}T00:00:00.000Z`);
      const dayEnd = new Date(`${day}T23:59:59.999Z`);
      
      const dayRequest = new sql.Request();
      dayRequest.input('dayStart', sql.DateTime, dayStart);
      dayRequest.input('dayEnd', sql.DateTime, dayEnd);
      
      const countResult = await dayRequest.query(`
        SELECT COUNT(*) as Count
        FROM tblParkwiz_Parking_Data_OLD 
        WHERE ExitDateTime BETWEEN @dayStart AND @dayEnd
      `);
      
      console.log(`- ${day}: ${countResult.recordset[0].Count} transactions`);
    }
    
    // Check for any data after June 21, 2025
    const afterJune21Request = new sql.Request();
    afterJune21Request.input('afterDate', sql.DateTime, new Date('2025-06-22T00:00:00.000Z'));
    
    const afterJune21Result = await afterJune21Request.query(`
      SELECT TOP 5
        PakringDataID,
        PlazaName,
        VehicleNumber,
        EntryDateTime,
        ExitDateTime,
        ParkedDuration,
        ParkingFee,
        iTotalGSTFee,
        PaymentMode
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime >= @afterDate
      ORDER BY ExitDateTime
    `);
    
    console.log('\nSample data after June 21, 2025 (if any):');
    if (afterJune21Result.recordset.length === 0) {
      console.log('No data found after June 21, 2025.');
    } else {
      console.log(JSON.stringify(afterJune21Result.recordset, null, 2));
    }

    await sql.close();
    console.log('\nDatabase connection closed.');
  } catch (err) {
    console.error('Error:', err);
    if (sql.connected) {
      await sql.close();
    }
  }
}

testDailyBreakdownJune2025();