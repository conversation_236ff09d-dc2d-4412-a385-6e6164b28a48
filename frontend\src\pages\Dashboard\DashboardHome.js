import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/authContext';
import { DashboardCard } from '../../components/DashboardCard';
import { Chart } from '../../components/Chart';
import { D3Chart } from '../../components/D3Chart';
import { RecentActivities } from '../../components/RecentActivities';
import { DateRangePicker } from '../../components/DateRangePicker';
import { EntityFilter } from '../../components/EntityFilter';
import { 
  CreditCard, 
  Clock, 
  Car, 
  DollarSign, 
  BarChart2, 
  Activity 
} from 'lucide-react';
import dashboardApi from '../../api/dashboardApi';

/**
 * DashboardHome Component
 * 
 * Main dashboard page showing key metrics and visualizations
 * based on user role and permissions
 */
const DashboardHome = () => {
  const { user, isSuperAdmin, isCompanyAdmin, isPlazaManager } = useAuth();
  const [dateRange, setDateRange] = useState('today');
  const [selectedEntity, setSelectedEntity] = useState({});
  const [chartType, setChartType] = useState('bar');
  const [dashboardData, setDashboardData] = useState({
    summary: {},
    revenueByPaymentMethod: [],
    recentTransactions: [],
    peakHours: [],
    loading: true,
    error: null
  });

  // Fetch dashboard data when filters change
  useEffect(() => {
    fetchDashboardData();
  }, [dateRange, selectedEntity]);

  // Function to fetch all dashboard data
  const fetchDashboardData = async () => {
    try {
      setDashboardData(prev => ({ ...prev, loading: true }));
      
      // Fetch summary data based on role
      const summaryResponse = await dashboardApi.getDashboardSummary({
        dateRange,
        ...selectedEntity
      });
      
      // Fetch revenue by payment method
      const paymentMethodResponse = await dashboardApi.getRevenueByPaymentMethod({
        dateRange,
        ...selectedEntity
      });
      
      // Fetch recent transactions
      const recentTransactionsResponse = await dashboardApi.getRecentTransactions({
        ...selectedEntity,
        limit: 5
      });
      
      // Fetch peak hours data
      const peakHoursResponse = await dashboardApi.getPeakHoursData({
        dateRange,
        ...selectedEntity
      });
      
      setDashboardData({
        summary: summaryResponse.data.data,
        revenueByPaymentMethod: paymentMethodResponse.data.data,
        recentTransactions: recentTransactionsResponse.data.data,
        peakHours: peakHoursResponse.data.data,
        loading: false,
        error: null
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setDashboardData(prev => ({ 
        ...prev, 
        loading: false, 
        error: 'Failed to load dashboard data' 
      }));
    }
  };

  // Format recent transactions for the activity feed
  const formatRecentActivities = () => {
    if (!dashboardData.recentTransactions) return [];
    
    return dashboardData.recentTransactions.map(transaction => ({
      title: `Vehicle ${transaction.VehicleNumber || 'Unknown'}`,
      time: new Date(transaction.ExitDateTime).toLocaleString(),
      value: `₹${(transaction.ParkingFee + transaction.iTotalGSTFee).toFixed(2)}`,
      color: getPaymentMethodColor(transaction.PaymentMode),
      details: `${transaction.PlazaName} - ${transaction.ExitLane}`
    }));
  };

  // Get color based on payment method
  const getPaymentMethodColor = (method) => {
    switch(method?.toLowerCase()) {
      case 'cash': return 'bg-green-500';
      case 'fastag': return 'bg-blue-500';
      case 'upi': return 'bg-purple-500';
      case 'card': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  // Render loading state
  if (dashboardData.loading) {
    return (
      <div className="p-6 flex justify-center items-center h-64">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-blue-200 rounded-full mb-4"></div>
          <div className="h-4 w-32 bg-blue-200 rounded mb-2"></div>
          <div className="h-3 w-24 bg-blue-100 rounded"></div>
        </div>
      </div>
    );
  }

  // Render error state
  if (dashboardData.error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p className="font-bold">Error</p>
          <p>{dashboardData.error}</p>
          <button 
            onClick={fetchDashboardData}
            className="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <main className="p-6">
      {/* Filters */}
      <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
        <h1 className="text-2xl font-bold">Dashboard</h1>
        <div className="flex flex-col sm:flex-row gap-4">
          <DateRangePicker value={dateRange} onChange={setDateRange} />
          {(isSuperAdmin() || isCompanyAdmin()) && (
            <EntityFilter 
              userRole={user.role} 
              selectedEntity={selectedEntity} 
              onChange={setSelectedEntity} 
            />
          )}
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <DashboardCard
          title="Total Revenue"
          value={`₹${dashboardData.summary.totalRevenue?.toLocaleString() || '0'}`}
          trend={dashboardData.summary.revenueTrend}
          icon={DollarSign}
          color="bg-blue-500" 
        />
        <DashboardCard
          title="Total Transactions"
          value={dashboardData.summary.transactionCount?.toLocaleString() || '0'}
          trend={dashboardData.summary.transactionTrend}
          icon={Activity}
          color="bg-green-500" 
        />
        <DashboardCard
          title="Vehicles Processed"
          value={dashboardData.summary.vehicleCount?.toLocaleString() || '0'}
          trend={dashboardData.summary.vehicleTrend}
          icon={Car}
          color="bg-purple-500" 
        />
        <DashboardCard
          title="Avg. Parking Duration"
          value={formatDuration(dashboardData.summary.avgDuration)}
          trend={dashboardData.summary.durationTrend}
          icon={Clock}
          color="bg-yellow-500" 
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Transaction Chart */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-sm p-6 dark:bg-gray-800 dark:text-white">
          {dashboardData.peakHours && dashboardData.peakHours.length > 0 ? (
            <>
              <div className="flex items-center justify-between mb-2">
                <h2 className="text-lg font-semibold">Transaction Overview</h2>
                <div className="flex space-x-2">
                  <button 
                    onClick={() => setChartType('line')}
                    className={`px-2 py-1 text-xs rounded ${chartType === 'line' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700'}`}
                  >
                    Line
                  </button>
                  <button 
                    onClick={() => setChartType('bar')}
                    className={`px-2 py-1 text-xs rounded ${chartType === 'bar' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700'}`}
                  >
                    Bar
                  </button>
                </div>
              </div>
              <D3Chart
                type={chartType}
                data={dashboardData.peakHours.map(hour => ({
                  label: `${hour.hour}:00`,
                  value: hour.count
                }))}
                options={{
                  height: 350,
                  xKey: "label",
                  yKey: "value",
                  animate: true,
                  showGrid: true,
                  curve: "cardinal",
                  barPadding: 0.2
                }}
              />
            </>
          ) : (
            <div className="flex justify-center items-center h-64 text-gray-500 dark:text-gray-400">
              No transaction data available for the selected period
            </div>
          )}
        </div>

        {/* Recent Activities */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Recent Transactions</h2>
          {dashboardData.recentTransactions && dashboardData.recentTransactions.length > 0 ? (
            <RecentActivities activities={formatRecentActivities()} />
          ) : (
            <div className="flex justify-center items-center h-64 text-gray-500">
              No recent transactions available
            </div>
          )}
        </div>
      </div>

      {/* Payment Method Distribution */}
      <div className="mt-6 bg-white rounded-xl shadow-sm p-6 dark:bg-gray-800 dark:text-white">
        {dashboardData.revenueByPaymentMethod && dashboardData.revenueByPaymentMethod.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* D3 Pie Chart */}
            <div>
              <D3Chart
                type="pie"
                data={dashboardData.revenueByPaymentMethod.map(method => ({
                  label: method.paymentMode || 'Unknown',
                  value: method.totalRevenue,
                  count: method.transactionCount
                }))}
                options={{
                  title: "Revenue by Payment Method",
                  height: 300,
                  xKey: "label",
                  yKey: "value",
                  donut: true,
                  showLegend: true
                }}
              />
            </div>
            
            {/* Payment Method Cards */}
            <div>
              <h2 className="text-lg font-semibold mb-4">Payment Details</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {dashboardData.revenueByPaymentMethod.map((method, index) => (
                  <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-2">
                      <div className={`p-2 rounded-md ${getPaymentMethodColor(method.paymentMode)}`}>
                        <CreditCard className="w-5 h-5 text-white" />
                      </div>
                      <h3 className="font-medium">{method.paymentMode || 'Unknown'}</h3>
                    </div>
                    <p className="text-2xl font-bold">₹{method.totalRevenue.toLocaleString()}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-300">{method.transactionCount} transactions</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex justify-center items-center h-32 text-gray-500 dark:text-gray-400">
            No payment data available for the selected period
          </div>
        )}
      </div>
    </main>
  );
};

// Helper function to format duration in hours and minutes
const formatDuration = (minutes) => {
  if (!minutes) return '0h 0m';
  const hours = Math.floor(minutes / 60);
  const mins = Math.round(minutes % 60);
  return `${hours}h ${mins}m`;
};

export default DashboardHome;
