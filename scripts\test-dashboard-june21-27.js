require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

/**
 * Test script for dashboard data between June 20-27, 2025
 * This script simulates the dashboard controller's functionality
 * for the specific date range: 2025-06-20T18:30:00.000Z to 2025-06-27T18:29:59.999Z
 */
async function testDashboardJune21to27() {
  try {
    console.log('Connecting to database...');
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });
    console.log('Connected successfully!');

    // Define the date range
    const startDate = new Date('2025-06-20T18:30:00.000Z');
    const endDate = new Date('2025-06-27T18:29:59.999Z');
    
    console.log(`\n=== DASHBOARD DATA ANALYSIS: JUNE 20-27, 2025 ===`);
    console.log(`Date range: ${startDate.toISOString()} to ${endDate.toISOString()}`);
    
    // Create a new SQL request
    const request = new sql.Request();
    request.input('startDate', sql.DateTime, startDate);
    request.input('endDate', sql.DateTime, endDate);
    
    // 1. DASHBOARD SUMMARY
    console.log('\n1. DASHBOARD SUMMARY');
    console.log('-------------------');
    
    const summaryResult = await request.query(`
      SELECT 
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as TotalRevenue, 
        COUNT(*) as TransactionCount,
        COUNT(DISTINCT CASE WHEN VehicleNumber <> 'NA' AND VehicleNumber IS NOT NULL THEN VehicleNumber ELSE NULL END) as VehicleCount,
        ISNULL(AVG(ISNULL(ParkedDuration, 0)), 0) as AvgDuration
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime BETWEEN @startDate AND @endDate
    `);
    
    const summary = summaryResult.recordset[0];
    console.log(`Total Revenue: ₹${summary.TotalRevenue.toFixed(2)}`);
    console.log(`Transaction Count: ${summary.TransactionCount}`);
    console.log(`Unique Vehicles: ${summary.VehicleCount}`);
    console.log(`Average Duration: ${summary.AvgDuration.toFixed(2)} minutes`);
    
    // 2. REVENUE BY PAYMENT METHOD
    console.log('\n2. REVENUE BY PAYMENT METHOD');
    console.log('---------------------------');
    
    const paymentMethodResult = await request.query(`
      SELECT 
        ISNULL(PaymentMode, 'Unknown') as PaymentMode, 
        COUNT(*) as TransactionCount, 
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as Revenue,
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) / 
          NULLIF(SUM(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0))) OVER(), 0) * 100 as Percentage
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime BETWEEN @startDate AND @endDate 
      GROUP BY PaymentMode 
      ORDER BY Revenue DESC
    `);
    
    paymentMethodResult.recordset.forEach(method => {
      console.log(`${method.PaymentMode}: ${method.TransactionCount} transactions, ₹${method.Revenue.toFixed(2)} (${method.Percentage ? method.Percentage.toFixed(2) : 0}%)`);
    });
    
    // 3. REVENUE BY PLAZA
    console.log('\n3. REVENUE BY PLAZA');
    console.log('------------------');
    
    const plazaResult = await request.query(`
      SELECT 
        PlazaName, 
        COUNT(*) as TransactionCount, 
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as Revenue,
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) / 
          NULLIF(SUM(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0))) OVER(), 0) * 100 as Percentage
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime BETWEEN @startDate AND @endDate 
      GROUP BY PlazaName 
      ORDER BY Revenue DESC
    `);
    
    plazaResult.recordset.forEach(plaza => {
      console.log(`${plaza.PlazaName}: ${plaza.TransactionCount} transactions, ₹${plaza.Revenue.toFixed(2)} (${plaza.Percentage ? plaza.Percentage.toFixed(2) : 0}%)`);
    });
    
    // 4. DAILY BREAKDOWN
    console.log('\n4. DAILY BREAKDOWN');
    console.log('------------------');
    
    const dailyResult = await request.query(`
      SELECT 
        CONVERT(date, ExitDateTime) as Date,
        COUNT(*) as TransactionCount,
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as Revenue,
        COUNT(DISTINCT CASE WHEN VehicleNumber <> 'NA' AND VehicleNumber IS NOT NULL THEN VehicleNumber ELSE NULL END) as VehicleCount
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime BETWEEN @startDate AND @endDate
      GROUP BY CONVERT(date, ExitDateTime)
      ORDER BY Date
    `);
    
    if (dailyResult.recordset.length === 0) {
      console.log('No data available for daily breakdown.');
    } else {
      dailyResult.recordset.forEach(day => {
        console.log(`${day.Date.toISOString().split('T')[0]}: ${day.TransactionCount} transactions, ₹${day.Revenue.toFixed(2)}, ${day.VehicleCount} vehicles`);
      });
    }
    
    // 5. PEAK HOURS ANALYSIS
    console.log('\n5. PEAK HOURS ANALYSIS');
    console.log('---------------------');
    
    const peakHoursResult = await request.query(`
      SELECT
        DATEPART(HOUR, ExitDateTime) as Hour,
        COUNT(*) as TransactionCount,
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as Revenue
      FROM tblParkwiz_Parking_Data_OLD
      WHERE ExitDateTime BETWEEN @startDate AND @endDate
      GROUP BY DATEPART(HOUR, ExitDateTime)
      ORDER BY TransactionCount DESC
    `);
    
    console.log('Top 5 busiest hours:');
    for (let i = 0; i < Math.min(5, peakHoursResult.recordset.length); i++) {
      const hour = peakHoursResult.recordset[i];
      console.log(`${hour.Hour.toString().padStart(2, '0')}:00 - ${hour.Hour.toString().padStart(2, '0')}:59: ${hour.TransactionCount} transactions, ₹${hour.Revenue.toFixed(2)}`);
    }
    
    // 6. VEHICLE TYPE ANALYSIS
    console.log('\n6. VEHICLE TYPE ANALYSIS');
    console.log('-----------------------');
    
    const vehicleTypeResult = await request.query(`
      SELECT 
        ISNULL(VehicleType, 'Unknown') as VehicleType,
        COUNT(*) as TransactionCount,
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as Revenue,
        ISNULL(AVG(ISNULL(ParkedDuration, 0)), 0) as AvgDuration
      FROM tblParkwiz_Parking_Data_OLD
      WHERE ExitDateTime BETWEEN @startDate AND @endDate
      GROUP BY VehicleType
      ORDER BY TransactionCount DESC
    `);
    
    vehicleTypeResult.recordset.forEach(type => {
      console.log(`${type.VehicleType}: ${type.TransactionCount} transactions, ₹${type.Revenue.toFixed(2)}, Avg Duration: ${type.AvgDuration.toFixed(2)} minutes`);
    });
    
    // 7. DATA COMPLETENESS CHECK
    console.log('\n7. DATA COMPLETENESS CHECK');
    console.log('--------------------------');
    
    // Define the day boundaries based on the specific time range
    const dayBoundaries = [
      { day: '2025-06-20', start: '2025-06-20T18:30:00.000Z', end: '2025-06-21T18:29:59.999Z' },
      { day: '2025-06-21', start: '2025-06-21T18:30:00.000Z', end: '2025-06-22T18:29:59.999Z' },
      { day: '2025-06-22', start: '2025-06-22T18:30:00.000Z', end: '2025-06-23T18:29:59.999Z' },
      { day: '2025-06-23', start: '2025-06-23T18:30:00.000Z', end: '2025-06-24T18:29:59.999Z' },
      { day: '2025-06-24', start: '2025-06-24T18:30:00.000Z', end: '2025-06-25T18:29:59.999Z' },
      { day: '2025-06-25', start: '2025-06-25T18:30:00.000Z', end: '2025-06-26T18:29:59.999Z' },
      { day: '2025-06-26', start: '2025-06-26T18:30:00.000Z', end: '2025-06-27T18:29:59.999Z' }
    ];
    
    console.log('Data availability by 24-hour period:');
    for (const period of dayBoundaries) {
      const periodStart = new Date(period.start);
      const periodEnd = new Date(period.end);
      
      const periodRequest = new sql.Request();
      periodRequest.input('periodStart', sql.DateTime, periodStart);
      periodRequest.input('periodEnd', sql.DateTime, periodEnd);
      
      const countResult = await periodRequest.query(`
        SELECT COUNT(*) as Count
        FROM tblParkwiz_Parking_Data_OLD 
        WHERE ExitDateTime BETWEEN @periodStart AND @periodEnd
      `);
      
      const status = countResult.recordset[0].Count > 0 ? 'Available' : 'Missing';
      console.log(`- ${period.day} (${periodStart.toISOString()} to ${periodEnd.toISOString()}): ${status} (${countResult.recordset[0].Count} transactions)`);
    }

    await sql.close();
    console.log('\nDatabase connection closed.');
  } catch (err) {
    console.error('Error:', err);
    if (sql.connected) {
      await sql.close();
    }
  }
}

testDashboardJune21to27();